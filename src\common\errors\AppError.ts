interface ResponseObject {
  success?: boolean
  name?: string
  message: unknown
  data?: unknown
  statusCode: number
}

export interface ErrorContext {
  cause?: unknown
  details?: Record<string, unknown>
  [key: string]: unknown
}

class AppError extends Error {
  public readonly statusCode: number
  public readonly data?: unknown
  public readonly context?: ErrorContext

  constructor(
    message?: string | string[],
    statusCode: number = 400,
    context?: ErrorContext
  ) {
    super(message instanceof Array ? message.join(', ') : message)

    this.name = this.constructor.name
    this.statusCode = statusCode
    if (context !== undefined) {
      this.context = context
    }

    // Ensure proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, new.target.prototype)

    // Capture stack trace if available
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor)
    }
  }

  json(): ResponseObject {
    const response: ResponseObject = {
      name: this.name,
      statusCode: this.statusCode,
      message: this.message
    }

    if (this.data !== null && typeof this.data !== 'undefined') {
      response.data = this.data
    }

    return response
  }
}

export default AppError
